import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppDispatch } from '../../hooks/redux';
import { useIsGameScanning } from '../../hooks/redux';
import { startGameScan, stopGameScan } from '../../store/slices/gameSlice';
import { GameList } from '../../components/game';
import { Modal } from '../../components/ui';
import { TroubleshootingPanel } from '../../components/connection';
import { useGameDetection } from '../../hooks';
import { GameInfo } from '../../services/game/GameDetector';
import GameDetector from '../../services/game/GameDetector';
import AchievementManager from '../../services/achievements/AchievementManager';
import LeaderboardManager from '../../services/leaderboards/LeaderboardManager';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

const { height } = Dimensions.get('window');

export default function GamesScreen() {
  const dispatch = useAppDispatch();
  const userProfile = useSelector((state: RootState) => (state as any).user?.profile);
  const isScanning = useIsGameScanning();

  // New game detection hook
  const {
    games,
    multiplayerGames,
    loading: gamesLoading,
    error,
    refreshGames,
  } = useGameDetection();

  // Local state
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);

  const handleGamePress = async (game: GameInfo) => {
    Alert.alert(
      game.name,
      `${game.description || 'No description available'}\n\nDeveloper: ${
        game.developer
      }\nCategory: ${game.category}`,
      [
        {
          text: 'Launch Game',
          onPress: () => launchGame(game),
        },
        {
          text: 'Create Session',
          onPress: () => createGameSession(game),
        },
        { text: 'Cancel', style: 'cancel' },
      ],
    );
  };

  const launchGame = async (game: GameInfo) => {
    try {
      console.log('Launching game:', game.name);

      // Initialize social services if user profile exists
      if (userProfile?.id) {
        await AchievementManager.initialize(userProfile.id);
        await LeaderboardManager.initialize(userProfile.id);

        // Track game session start
        await AchievementManager.trackAction('game_session_started', 1, {
          gameId: game.packageName,
        });
      }

      // Use GameDetector's launch functionality
      const launchResult = await GameDetector.launchGame(game.packageName);

      if (launchResult.success) {
        // Update game launch stats
        await GameDetector.updateGamePlaytime(game.packageName, 0); // Start tracking

        Alert.alert(
          'Game Launched',
          `${game.name} launched successfully!\n\nSession tracking started.`,
          [
            { text: 'OK' },
            {
              text: 'View Profile',
              onPress: () => {
                // Navigate to profile to see updated stats
                // router.push('/profile/');
              },
            },
          ],
        );
      } else {
        Alert.alert('Launch Failed', launchResult.error || 'Failed to launch game', [
          { text: 'OK' },
          {
            text: 'Troubleshoot',
            onPress: () => setShowTroubleshooting(true),
          },
        ]);
      }
    } catch (error) {
      console.error('Failed to launch game:', error);
      Alert.alert('Error', 'Failed to launch game');
    }
  };

  const createGameSession = async (game: GameInfo) => {
    try {
      // Initialize social services
      if (userProfile?.id) {
        await AchievementManager.initialize(userProfile.id);

        // Track session creation
        await AchievementManager.trackSessionHosted();
      }

      Alert.alert(
        'Session Created',
        `Created a multiplayer session for ${game.name}!\n\nOther players can now join your session.`,
      );
    } catch (error) {
      console.error('Failed to create session:', error);
      Alert.alert('Error', 'Failed to create session');
    }
  };

  const handleGameLongPress = (game: GameInfo) => {
    Alert.alert('Game Options', `What would you like to do with ${game.name}?`, [
      { text: 'View Details', onPress: () => handleGamePress(game) },
      { text: 'Troubleshoot', onPress: () => setShowTroubleshooting(true) },
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#1a1a2e', '#16213e', '#0f3460']} style={styles.background} />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>Games</Text>
            <Text style={styles.subtitle}>
              {games.length} games • {multiplayerGames.length} multiplayer
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.troubleshootButton}
              onPress={() => setShowTroubleshooting(true)}
            >
              <Ionicons name="medical" size={20} color="#00D4FF" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.scanButton}
              onPress={() => {
                dispatch(isScanning ? stopGameScan() : startGameScan());
                refreshGames();
              }}
            >
              <Ionicons
                name={gamesLoading || isScanning ? 'stop' : 'refresh'}
                size={24}
                color="#00D4FF"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Game Detection Status */}
        {(gamesLoading || isScanning) && (
          <View style={styles.loadingStatusContainer}>
            <Text style={styles.loadingStatusText}>
              {isScanning ? 'Scanning for games...' : 'Loading games...'}
            </Text>
          </View>
        )}

        {error && (
          <View style={styles.errorContainer}>
            <Ionicons name="warning" size={20} color="#FF4757" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* Game List Component */}
        <GameList
          games={games}
          loading={gamesLoading}
          onRefresh={refreshGames}
          onGamePress={handleGamePress}
          onGameLongPress={handleGameLongPress}
          showSearch={true}
          showFilter={true}
          showCompatibility={true}
          emptyMessage="No games found. Install some games and refresh!"
          style={styles.gameListContainer}
        />
      </SafeAreaView>

      {/* Troubleshooting Modal */}
      <Modal
        visible={showTroubleshooting}
        onClose={() => setShowTroubleshooting(false)}
        variant="fullscreen"
      >
        <TroubleshootingPanel onClose={() => setShowTroubleshooting(false)} />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 4,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  troubleshootButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanButton: {
    padding: 8,
  },
  gameListContainer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  filterTabActive: {
    backgroundColor: '#00D4FF',
  },
  filterText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  filterTextActive: {
    color: '#1a1a2e',
    fontWeight: '600',
  },
  gamesList: {
    paddingHorizontal: 20,
    gap: 12,
  },
  gameCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  gameBlur: {
    padding: 16,
  },
  gameContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  gameIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  gameInfo: {
    flex: 1,
  },
  gameName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  gamePlayers: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  gameAction: {
    padding: 8,
  },
  addGameButton: {
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  addGameBlur: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    gap: 12,
  },
  addGameText: {
    fontSize: 16,
    color: '#00D4FF',
    fontWeight: '600',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 71, 87, 0.1)',
    marginHorizontal: 20,
    marginVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 71, 87, 0.3)',
  },
  errorText: {
    fontSize: 14,
    color: '#FF4757',
    marginLeft: 8,
    flex: 1,
  },
  loadingStatusContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingStatusText: {
    fontSize: 14,
    color: '#00D4FF',
    fontWeight: '600',
  },
});
