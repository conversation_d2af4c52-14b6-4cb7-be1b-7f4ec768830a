import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Switch,
  Alert,
  Platform,
  Linking,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import {
  Modal,
  Input,
  Button,
  SettingsPicker,
  StorageUsageChart,
} from "../../components/ui";
import { TroubleshootingPanel } from "../../components/connection";
import UserPreferences from "../../services/UserPreferences";
import DeviceInfoService from "../../services/DeviceInfoService";
import AppStoreRatingService from "../../services/AppStoreRatingService";
import NotificationService from "../../services/NotificationService";
import { useAppDispatch, useAppSelector } from "../../hooks/redux";
import {
  updateSettings,
  setTheme,
  updateNotificationSettings,
  updateConnectionSettings,
  updatePrivacySettings,
} from "../../store/slices/settingsSlice";
import {
  ConnectionTimeoutOptions,
  ThemeColorOptions,
  DeviceVisibilityOptions,
} from "../../components/ui/SettingsPicker";

const { width, height } = Dimensions.get("window");

export default function SettingsScreen() {
  // Redux state
  const dispatch = useAppDispatch();
  const reduxSettings = useAppSelector(
    (state) => state.settings?.settings || {}
  );

  // Local state
  const [deviceName, setDeviceName] = useState("");
  const [userStats, setUserStats] = useState({ sessions: 0, playtime: 0 });
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);
  const [notificationPermissions, setNotificationPermissions] =
    useState<any>(null);

  // Modal states
  const [showEditName, setShowEditName] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const [showConnectionTimeout, setShowConnectionTimeout] = useState(false);
  const [showThemeColor, setShowThemeColor] = useState(false);
  const [showDeviceVisibility, setShowDeviceVisibility] = useState(false);
  const [showStorageDetails, setShowStorageDetails] = useState(false);
  const [showPerformanceDetails, setShowPerformanceDetails] = useState(false);
  const [showNotificationSettings, setShowNotificationSettings] =
    useState(false);

  // Form states
  const [newDeviceName, setNewDeviceName] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadPreferences();
    loadDeviceInfo();
    initializeServices();
  }, []);

  const loadPreferences = async () => {
    try {
      const prefs = await UserPreferences.getPreferences();
      setDeviceName(prefs.deviceName);
      setNewDeviceName(prefs.deviceName);
      setUserStats({
        sessions: prefs.totalSessions,
        playtime: prefs.totalPlaytime,
      });

      // Update Redux store with current preferences
      dispatch(
        updateSettings({
          theme: prefs.theme,
          notifications: {
            ...reduxSettings.notifications,
            enabled: prefs.notificationsEnabled,
          },
          connection: {
            ...reduxSettings.connection,
            autoConnect: prefs.autoScanEnabled,
          },
        })
      );
    } catch (error) {
      console.error("Failed to load preferences:", error);
    }
  };

  const loadDeviceInfo = async () => {
    try {
      setIsLoading(true);
      const [deviceData, storageData, performanceData] = await Promise.all([
        DeviceInfoService.getDeviceInfo(),
        DeviceInfoService.getStorageInfo(),
        DeviceInfoService.getPerformanceMetrics(),
      ]);

      setDeviceInfo(deviceData);
      setStorageInfo(storageData);
      setPerformanceMetrics(performanceData);
    } catch (error) {
      console.error("Failed to load device info:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const initializeServices = async () => {
    try {
      // Initialize notification service
      await NotificationService.initialize();
      const permissions = await NotificationService.checkPermissions();
      setNotificationPermissions(permissions);

      // Initialize app store rating service
      await AppStoreRatingService.initialize();
      await AppStoreRatingService.incrementSessionCount();
    } catch (error) {
      console.error("Failed to initialize services:", error);
    }
  };

  // Settings handlers
  const handleNotificationsToggle = async (value: boolean) => {
    dispatch(updateNotificationSettings({ enabled: value }));
    await UserPreferences.setNotificationsEnabled(value);
    await NotificationService.setEnabled(value);
  };

  const handleAutoConnectToggle = async (value: boolean) => {
    dispatch(updateConnectionSettings({ autoConnect: value }));
    await UserPreferences.setAutoScanEnabled(value);
  };

  const handleDarkModeToggle = async (value: boolean) => {
    const theme = value ? "dark" : "light";
    dispatch(setTheme(theme));
    await UserPreferences.setTheme(theme);
  };

  const handleAnalyticsToggle = async (value: boolean) => {
    // Update analytics setting in Redux and UserPreferences
    dispatch(
      updateSettings({
        privacy: {
          ...reduxSettings.privacy,
          shareGameActivity: value,
        },
      })
    );
  };

  // Device and storage handlers
  const handleSaveDeviceName = async () => {
    if (newDeviceName.trim()) {
      await UserPreferences.setDeviceName(newDeviceName.trim());
      setDeviceName(newDeviceName.trim());
      setShowEditName(false);
    }
  };

  const handleClearCache = async () => {
    try {
      setIsLoading(true);
      const success = await DeviceInfoService.clearCache();
      if (success) {
        Alert.alert("Success", "Cache cleared successfully");
        await loadDeviceInfo(); // Refresh storage info
      } else {
        Alert.alert("Error", "Failed to clear cache");
      }
    } catch (error) {
      Alert.alert("Error", "Failed to clear cache");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOptimizeStorage = async () => {
    try {
      setIsLoading(true);
      const success = await DeviceInfoService.optimizeStorage();
      if (success) {
        Alert.alert("Success", "Storage optimized successfully");
        await loadDeviceInfo(); // Refresh storage info
      } else {
        Alert.alert("Error", "Failed to optimize storage");
      }
    } catch (error) {
      Alert.alert("Error", "Failed to optimize storage");
    } finally {
      setIsLoading(false);
    }
  };

  // App store rating handler
  const handleRateApp = async () => {
    try {
      await AppStoreRatingService.forceShowRatingPrompt();
    } catch (error) {
      Alert.alert("Error", "Unable to open app store");
    }
  };

  // Connection timeout handler
  const handleConnectionTimeoutSelect = async (value: string) => {
    const timeout = parseInt(value) * 1000; // Convert to milliseconds
    dispatch(updateConnectionSettings({ timeout }));
  };

  // Theme color handler
  const handleThemeColorSelect = async (value: string) => {
    dispatch(updateSettings({ accentColor: value }));
  };

  // Device visibility handler
  const handleDeviceVisibilitySelect = async (value: string) => {
    dispatch(
      updatePrivacySettings({
        deviceVisibility: value as "everyone" | "contacts" | "nobody",
      })
    );
  };

  const handleResetPreferences = () => {
    Alert.alert(
      "Reset Settings",
      "This will reset all your preferences and clear your data. This action cannot be undone.",
      [
        {
          text: "Reset",
          style: "destructive",
          onPress: async () => {
            await UserPreferences.resetPreferences();
            Alert.alert(
              "Settings Reset",
              "All preferences have been reset to defaults."
            );
            loadPreferences();
          },
        },
        { text: "Cancel", style: "cancel" },
      ]
    );
  };

  const formatPlaytime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const settingsGroups = [
    {
      title: "Your Device",
      items: [
        {
          icon: "phone-portrait",
          title: "Device Name",
          subtitle: deviceName || "Tap to set device name",
          type: "action",
          onPress: () => setShowEditName(true),
        },
        {
          icon: "hardware-chip",
          title: "Device Information",
          subtitle: deviceInfo
            ? `${deviceInfo.modelName} • ${deviceInfo.osName} ${deviceInfo.osVersion}`
            : "Loading...",
          type: "navigation",
          onPress: () => setShowStorageDetails(true),
        },
        {
          icon: "server",
          title: "Storage Management",
          subtitle: storageInfo
            ? `${DeviceInfoService.formatBytes(
                storageInfo.usedSpace
              )} of ${DeviceInfoService.formatBytes(
                storageInfo.totalSpace
              )} used`
            : "Loading...",
          type: "navigation",
          onPress: () => setShowStorageDetails(true),
        },
        {
          icon: "speedometer",
          title: "Performance",
          subtitle: performanceMetrics
            ? `${performanceMetrics.frameRate} FPS • ${performanceMetrics.memoryUsage}% Memory`
            : "Loading...",
          type: "navigation",
          onPress: () => setShowPerformanceDetails(true),
        },
        {
          icon: "stats-chart",
          title: "Gaming Sessions",
          subtitle: `${userStats.sessions} sessions completed`,
          type: "info",
        },
        {
          icon: "time",
          title: "Total Playtime",
          subtitle: formatPlaytime(userStats.playtime),
          type: "info",
        },
      ],
    },
    {
      title: "Connection",
      items: [
        {
          icon: "notifications",
          title: "Notifications",
          subtitle: reduxSettings.notifications?.enabled
            ? "Enabled"
            : "Disabled",
          type: "switch",
          value: reduxSettings.notifications?.enabled || false,
          onToggle: handleNotificationsToggle,
        },
        {
          icon: "settings",
          title: "Notification Settings",
          subtitle: "Configure notification preferences",
          type: "navigation",
          onPress: () => setShowNotificationSettings(true),
        },
        {
          icon: "flash",
          title: "Auto Connect",
          subtitle: "Automatically connect to known devices",
          type: "switch",
          value: reduxSettings.connection?.autoConnect || false,
          onToggle: handleAutoConnectToggle,
        },
        {
          icon: "time",
          title: "Connection Timeout",
          subtitle: `${
            (reduxSettings.connection?.timeout || 30000) / 1000
          } seconds`,
          type: "navigation",
          onPress: () => setShowConnectionTimeout(true),
        },
      ],
    },
    {
      title: "Appearance",
      items: [
        {
          icon: "moon",
          title: "Dark Mode",
          subtitle: "Use dark theme",
          type: "switch",
          value: reduxSettings.theme === "dark",
          onToggle: handleDarkModeToggle,
        },
        {
          icon: "color-palette",
          title: "Theme Color",
          subtitle: "Customize app accent color",
          type: "navigation",
          onPress: () => setShowThemeColor(true),
        },
      ],
    },
    {
      title: "Privacy & Security",
      items: [
        {
          icon: "shield-checkmark",
          title: "Device Visibility",
          subtitle:
            `${reduxSettings.privacy?.deviceVisibility || "everyone"}`
              .charAt(0)
              .toUpperCase() +
            `${reduxSettings.privacy?.deviceVisibility || "everyone"}`.slice(1),
          type: "navigation",
          onPress: () => setShowDeviceVisibility(true),
        },
        {
          icon: "analytics",
          title: "Analytics",
          subtitle: "Help improve the app",
          type: "switch",
          value: reduxSettings.privacy?.shareGameActivity || false,
          onToggle: handleAnalyticsToggle,
        },
        {
          icon: "lock-closed",
          title: "Privacy Policy",
          subtitle: "View our privacy policy",
          type: "navigation",
          onPress: () => Linking.openURL("https://yourapp.com/privacy"),
        },
      ],
    },
    {
      title: "Support",
      items: [
        {
          icon: "medical",
          title: "Network Diagnostics",
          subtitle: "Run network troubleshooting",
          type: "action",
          onPress: () => setShowTroubleshooting(true),
        },
        {
          icon: "help-circle",
          title: "Help & FAQ",
          subtitle: "Get help and find answers",
          type: "navigation",
          onPress: () => Linking.openURL("https://yourapp.com/help"),
        },
        {
          icon: "refresh",
          title: "Reset Settings",
          subtitle: "Reset all preferences to defaults",
          type: "action",
          onPress: handleResetPreferences,
        },
        {
          icon: "bug",
          title: "Report a Bug",
          subtitle: "Help us improve the app",
          type: "navigation",
          onPress: () =>
            Linking.openURL("mailto:<EMAIL>?subject=Bug Report"),
        },
        {
          icon: "star",
          title: "Rate the App",
          subtitle: "Share your feedback",
          type: "action",
          onPress: handleRateApp,
        },
        {
          icon: "information-circle",
          title: "About",
          subtitle: "App version and information",
          type: "navigation",
          onPress: () =>
            Alert.alert(
              "About LoGaCo",
              `Version: 1.0.0\nBuild: ${
                Platform.OS === "ios" ? "iOS" : "Android"
              }\n\nLocal Game Connect - Connect and play with nearby devices.`
            ),
        },
      ],
    },
  ];

  const renderSettingItem = (item: any) => {
    const handlePress = () => {
      if (item.onPress) {
        item.onPress();
      }
    };

    return (
      <TouchableOpacity
        key={item.title}
        style={styles.settingItem}
        onPress={handlePress}
        disabled={item.type === "info"}
        activeOpacity={item.type === "info" ? 1 : 0.7}
      >
        <View style={styles.settingIcon}>
          <Ionicons name={item.icon} size={20} color="#00D4FF" />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
        </View>
        <View style={styles.settingAction}>
          {item.type === "switch" ? (
            <Switch
              value={item.value}
              onValueChange={item.onToggle}
              trackColor={{ false: "#767577", true: "#00D4FF" }}
              thumbColor={item.value ? "#FFFFFF" : "#f4f3f4"}
            />
          ) : item.type === "info" ? (
            <View style={styles.infoIndicator} />
          ) : (
            <Ionicons
              name="chevron-forward"
              size={20}
              color="rgba(255, 255, 255, 0.4)"
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Settings</Text>
          </View>

          {/* Profile Section */}
          <BlurView intensity={15} style={styles.profileCard}>
            <View style={styles.profileContent}>
              <View style={styles.avatar}>
                <Ionicons name="person" size={32} color="#00D4FF" />
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>Your Device</Text>
                <Text style={styles.profileSubtitle}>iPhone 15 Pro</Text>
              </View>
              <TouchableOpacity style={styles.editButton}>
                <Ionicons name="pencil" size={20} color="#00D4FF" />
              </TouchableOpacity>
            </View>
          </BlurView>

          {/* Settings Groups */}
          {settingsGroups.map((group) => (
            <View key={group.title} style={styles.settingsGroup}>
              <Text style={styles.groupTitle}>{group.title}</Text>
              <BlurView intensity={15} style={styles.groupCard}>
                {group.items.map((item, index) => (
                  <View key={item.title}>
                    {renderSettingItem(item)}
                    {index < group.items.length - 1 && (
                      <View style={styles.separator} />
                    )}
                  </View>
                ))}
              </BlurView>
            </View>
          ))}

          {/* App Info */}
          <View style={styles.appInfo}>
            <Text style={styles.appVersion}>LoGaCo v1.0.0</Text>
            <Text style={styles.appSubtitle}>Local Game Connect</Text>
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* Edit Device Name Modal */}
      <Modal
        visible={showEditName}
        onClose={() => setShowEditName(false)}
        title="Edit Device Name"
        variant="center"
      >
        <View style={styles.editNameContent}>
          <Input
            label="Device Name"
            value={newDeviceName}
            onChangeText={setNewDeviceName}
            placeholder="Enter device name"
            maxLength={20}
          />
          <View style={styles.editNameActions}>
            <Button
              title="Cancel"
              onPress={() => setShowEditName(false)}
              variant="ghost"
              size="medium"
              style={styles.editNameButton}
            />
            <Button
              title="Save"
              onPress={handleSaveDeviceName}
              variant="primary"
              size="medium"
              style={styles.editNameButton}
            />
          </View>
        </View>
      </Modal>

      {/* Troubleshooting Modal */}
      <Modal
        visible={showTroubleshooting}
        onClose={() => setShowTroubleshooting(false)}
        variant="fullscreen"
      >
        <TroubleshootingPanel onClose={() => setShowTroubleshooting(false)} />
      </Modal>

      {/* Connection Timeout Picker */}
      <SettingsPicker
        visible={showConnectionTimeout}
        onClose={() => setShowConnectionTimeout(false)}
        title="Connection Timeout"
        subtitle="How long to wait for connections"
        options={ConnectionTimeoutOptions}
        selectedValue={(
          (reduxSettings.connection?.timeout || 30000) / 1000
        ).toString()}
        onSelect={handleConnectionTimeoutSelect}
      />

      {/* Theme Color Picker */}
      <SettingsPicker
        visible={showThemeColor}
        onClose={() => setShowThemeColor(false)}
        title="Theme Color"
        subtitle="Choose your preferred accent color"
        options={ThemeColorOptions}
        selectedValue={reduxSettings.accentColor || "#00D4FF"}
        onSelect={handleThemeColorSelect}
      />

      {/* Device Visibility Picker */}
      <SettingsPicker
        visible={showDeviceVisibility}
        onClose={() => setShowDeviceVisibility(false)}
        title="Device Visibility"
        subtitle="Control who can see your device"
        options={DeviceVisibilityOptions}
        selectedValue={reduxSettings.privacy?.deviceVisibility || "everyone"}
        onSelect={handleDeviceVisibilitySelect}
      />

      {/* Storage Details Modal */}
      <Modal
        visible={showStorageDetails}
        onClose={() => setShowStorageDetails(false)}
        title="Storage Management"
        variant="center"
      >
        {storageInfo && (
          <StorageUsageChart
            storageInfo={storageInfo}
            onOptimize={handleOptimizeStorage}
            onClearCache={handleClearCache}
          />
        )}
      </Modal>

      {/* Performance Details Modal */}
      <Modal
        visible={showPerformanceDetails}
        onClose={() => setShowPerformanceDetails(false)}
        title="Performance Metrics"
        variant="center"
      >
        {performanceMetrics && (
          <View style={styles.performanceContent}>
            <View style={styles.metricItem}>
              <Text style={styles.metricLabel}>Frame Rate</Text>
              <Text style={styles.metricValue}>
                {performanceMetrics.frameRate} FPS
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricLabel}>Memory Usage</Text>
              <Text style={styles.metricValue}>
                {performanceMetrics.memoryUsage}%
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricLabel}>CPU Usage</Text>
              <Text style={styles.metricValue}>
                {performanceMetrics.cpuUsage}%
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricLabel}>Battery Usage</Text>
              <Text style={styles.metricValue}>
                {performanceMetrics.batteryUsage}%
              </Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricLabel}>App Startup Time</Text>
              <Text style={styles.metricValue}>
                {(performanceMetrics.appStartupTime / 1000).toFixed(1)}s
              </Text>
            </View>
          </View>
        )}
      </Modal>

      {/* Notification Settings Modal */}
      <Modal
        visible={showNotificationSettings}
        onClose={() => setShowNotificationSettings(false)}
        title="Notification Settings"
        variant="center"
      >
        <View style={styles.notificationContent}>
          <Text style={styles.notificationText}>
            Configure your notification preferences in the device settings.
          </Text>
          <Button
            title="Open Settings"
            onPress={() => {
              NotificationService.openNotificationSettings();
              setShowNotificationSettings(false);
            }}
            variant="primary"
            size="medium"
          />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  profileCard: {
    marginHorizontal: 20,
    marginBottom: 30,
    borderRadius: 16,
    overflow: "hidden",
    padding: 20,
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  profileSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  editButton: {
    padding: 8,
  },
  settingsGroup: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "rgba(255, 255, 255, 0.8)",
    marginBottom: 12,
    marginLeft: 4,
  },
  groupCard: {
    borderRadius: 16,
    overflow: "hidden",
    padding: 4,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#FFFFFF",
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  settingAction: {
    marginLeft: 16,
  },
  separator: {
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    marginLeft: 68,
  },
  appInfo: {
    alignItems: "center",
    paddingVertical: 20,
  },
  appVersion: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  appSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  editNameContent: {
    gap: 20,
  },
  editNameActions: {
    flexDirection: "row",
    gap: 12,
  },
  editNameButton: {
    flex: 1,
  },
  infoIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  performanceContent: {
    gap: 16,
  },
  metricItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 8,
  },
  metricLabel: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
  },
  metricValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#00D4FF",
  },
  notificationContent: {
    gap: 20,
    alignItems: "center",
  },
  notificationText: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
    lineHeight: 24,
  },
});
