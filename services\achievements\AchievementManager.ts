import { EventEmitter } from 'events';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  type: AchievementType;
  criteria: AchievementCriteria;
  reward: AchievementReward;
  rarity: AchievementRarity;
  isUnlocked: boolean;
  unlockedAt?: number;
  progress: number;
  maxProgress: number;
  isHidden: boolean;
}

export type AchievementCategory = 
  | 'connection' 
  | 'gaming' 
  | 'social' 
  | 'technical' 
  | 'milestone' 
  | 'special';

export type AchievementType = 
  | 'counter' 
  | 'milestone' 
  | 'streak' 
  | 'time_based' 
  | 'conditional';

export type AchievementRarity = 
  | 'common' 
  | 'uncommon' 
  | 'rare' 
  | 'epic' 
  | 'legendary';

export interface AchievementCriteria {
  action: string;
  target: number;
  timeframe?: number; // in milliseconds
  conditions?: Record<string, any>;
}

export interface AchievementReward {
  points: number;
  title?: string;
  badge?: string;
  unlocks?: string[];
}

export interface UserAchievementProgress {
  userId: string;
  achievements: Map<string, Achievement>;
  totalPoints: number;
  unlockedCount: number;
  lastUpdated: number;
}

class AchievementManager extends EventEmitter {
  private static instance: AchievementManager;
  private isInitialized: boolean = false;
  private achievements: Map<string, Achievement> = new Map();
  private userProgress: UserAchievementProgress | null = null;
  private actionCounters: Map<string, number> = new Map();
  private streakCounters: Map<string, { count: number; lastAction: number }> = new Map();

  private constructor() {
    super();
  }

  static getInstance(): AchievementManager {
    if (!AchievementManager.instance) {
      AchievementManager.instance = new AchievementManager();
    }
    return AchievementManager.instance;
  }

  async initialize(userId: string): Promise<void> {
    try {
      console.log('Initializing Achievement Manager...');
      
      // Load achievement definitions
      this.loadAchievementDefinitions();
      
      // Load user progress
      await this.loadUserProgress(userId);
      
      this.isInitialized = true;
      this.emit('initialized');
      console.log('Achievement Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Achievement Manager:', error);
      throw error;
    }
  }

  private loadAchievementDefinitions(): void {
    const definitions: Achievement[] = [
      // Connection Achievements
      {
        id: 'first_connection',
        name: 'First Contact',
        description: 'Make your first connection with another device',
        icon: 'link',
        category: 'connection',
        type: 'milestone',
        criteria: { action: 'device_connected', target: 1 },
        reward: { points: 100, title: 'Connector' },
        rarity: 'common',
        isUnlocked: false,
        progress: 0,
        maxProgress: 1,
        isHidden: false
      },
      {
        id: 'social_butterfly',
        name: 'Social Butterfly',
        description: 'Connect to 10 different devices',
        icon: 'people',
        category: 'connection',
        type: 'counter',
        criteria: { action: 'unique_device_connected', target: 10 },
        reward: { points: 500, title: 'Social Butterfly' },
        rarity: 'uncommon',
        isUnlocked: false,
        progress: 0,
        maxProgress: 10,
        isHidden: false
      },
      {
        id: 'network_master',
        name: 'Network Master',
        description: 'Successfully connect via all connection methods',
        icon: 'wifi',
        category: 'technical',
        type: 'conditional',
        criteria: { 
          action: 'connection_methods_used', 
          target: 3,
          conditions: { methods: ['bluetooth', 'wifi', 'qr'] }
        },
        reward: { points: 750, title: 'Network Master', badge: 'network_expert' },
        rarity: 'rare',
        isUnlocked: false,
        progress: 0,
        maxProgress: 3,
        isHidden: false
      },

      // Gaming Achievements
      {
        id: 'first_game',
        name: 'Game On!',
        description: 'Start your first gaming session',
        icon: 'game-controller',
        category: 'gaming',
        type: 'milestone',
        criteria: { action: 'game_session_started', target: 1 },
        reward: { points: 150, title: 'Gamer' },
        rarity: 'common',
        isUnlocked: false,
        progress: 0,
        maxProgress: 1,
        isHidden: false
      },
      {
        id: 'marathon_gamer',
        name: 'Marathon Gamer',
        description: 'Play for 5 hours total',
        icon: 'time',
        category: 'gaming',
        type: 'counter',
        criteria: { action: 'playtime_minutes', target: 300 },
        reward: { points: 1000, title: 'Marathon Gamer' },
        rarity: 'epic',
        isUnlocked: false,
        progress: 0,
        maxProgress: 300,
        isHidden: false
      },
      {
        id: 'game_collector',
        name: 'Game Collector',
        description: 'Have 20 compatible games installed',
        icon: 'library',
        category: 'gaming',
        type: 'counter',
        criteria: { action: 'compatible_games_count', target: 20 },
        reward: { points: 600, title: 'Collector' },
        rarity: 'uncommon',
        isUnlocked: false,
        progress: 0,
        maxProgress: 20,
        isHidden: false
      },

      // Social Achievements
      {
        id: 'party_host',
        name: 'Party Host',
        description: 'Host 5 gaming sessions',
        icon: 'people-circle',
        category: 'social',
        type: 'counter',
        criteria: { action: 'sessions_hosted', target: 5 },
        reward: { points: 400, title: 'Host' },
        rarity: 'uncommon',
        isUnlocked: false,
        progress: 0,
        maxProgress: 5,
        isHidden: false
      },
      {
        id: 'team_player',
        name: 'Team Player',
        description: 'Join 10 gaming sessions',
        icon: 'person-add',
        category: 'social',
        type: 'counter',
        criteria: { action: 'sessions_joined', target: 10 },
        reward: { points: 350, title: 'Team Player' },
        rarity: 'common',
        isUnlocked: false,
        progress: 0,
        maxProgress: 10,
        isHidden: false
      },

      // Milestone Achievements
      {
        id: 'golden_winner',
        name: 'Golden Winner',
        description: 'Win 10 gaming sessions',
        icon: 'trophy',
        category: 'milestone',
        type: 'counter',
        criteria: { action: 'sessions_won', target: 10 },
        reward: { points: 1500, title: '2 time golden winner', badge: 'gold_trophy' },
        rarity: 'epic',
        isUnlocked: false,
        progress: 0,
        maxProgress: 10,
        isHidden: false
      },
      {
        id: 'silver_achiever',
        name: 'Silver Achiever',
        description: 'Come in 2nd place 6 times',
        icon: 'medal',
        category: 'milestone',
        type: 'counter',
        criteria: { action: 'second_place_finishes', target: 6 },
        reward: { points: 800, title: '6 time silver winner', badge: 'silver_medal' },
        rarity: 'rare',
        isUnlocked: false,
        progress: 0,
        maxProgress: 6,
        isHidden: false
      },

      // Special Achievements
      {
        id: 'early_adopter',
        name: 'Early Adopter',
        description: 'Use the app within the first week of installation',
        icon: 'star',
        category: 'special',
        type: 'time_based',
        criteria: { action: 'app_usage', target: 1, timeframe: 7 * 24 * 60 * 60 * 1000 },
        reward: { points: 2000, title: 'Early Adopter', badge: 'pioneer' },
        rarity: 'legendary',
        isUnlocked: false,
        progress: 0,
        maxProgress: 1,
        isHidden: true
      }
    ];

    definitions.forEach(achievement => {
      this.achievements.set(achievement.id, achievement);
    });

    console.log(`Loaded ${definitions.length} achievement definitions`);
  }

  private async loadUserProgress(userId: string): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(`achievements_${userId}`);
      
      if (stored) {
        const data = JSON.parse(stored);
        this.userProgress = {
          userId,
          achievements: new Map(data.achievements),
          totalPoints: data.totalPoints || 0,
          unlockedCount: data.unlockedCount || 0,
          lastUpdated: data.lastUpdated || Date.now()
        };
      } else {
        // Initialize new user progress
        this.userProgress = {
          userId,
          achievements: new Map(),
          totalPoints: 0,
          unlockedCount: 0,
          lastUpdated: Date.now()
        };

        // Copy achievement definitions to user progress
        this.achievements.forEach((achievement, id) => {
          this.userProgress!.achievements.set(id, { ...achievement });
        });
      }

      console.log(`Loaded user progress: ${this.userProgress.unlockedCount} achievements unlocked`);
    } catch (error) {
      console.error('Failed to load user progress:', error);
      throw error;
    }
  }

  async saveUserProgress(): Promise<void> {
    if (!this.userProgress) return;

    try {
      const data = {
        achievements: Array.from(this.userProgress.achievements.entries()),
        totalPoints: this.userProgress.totalPoints,
        unlockedCount: this.userProgress.unlockedCount,
        lastUpdated: Date.now()
      };

      await AsyncStorage.setItem(
        `achievements_${this.userProgress.userId}`,
        JSON.stringify(data)
      );
    } catch (error) {
      console.error('Failed to save user progress:', error);
    }
  }

  // Track user actions for achievement progress
  async trackAction(action: string, value: number = 1, metadata?: Record<string, any>): Promise<void> {
    if (!this.isInitialized || !this.userProgress) return;

    try {
      // Update action counters
      const currentCount = this.actionCounters.get(action) || 0;
      this.actionCounters.set(action, currentCount + value);

      // Check achievements for this action
      const relevantAchievements = Array.from(this.userProgress.achievements.values())
        .filter(achievement => 
          !achievement.isUnlocked && 
          achievement.criteria.action === action
        );

      for (const achievement of relevantAchievements) {
        await this.updateAchievementProgress(achievement, value, metadata);
      }

      await this.saveUserProgress();
    } catch (error) {
      console.error('Failed to track action:', error);
    }
  }

  private async updateAchievementProgress(
    achievement: Achievement, 
    value: number, 
    metadata?: Record<string, any>
  ): Promise<void> {
    if (!this.userProgress) return;

    const userAchievement = this.userProgress.achievements.get(achievement.id);
    if (!userAchievement || userAchievement.isUnlocked) return;

    // Update progress based on achievement type
    switch (achievement.type) {
      case 'counter':
      case 'milestone':
        userAchievement.progress = Math.min(
          userAchievement.progress + value,
          userAchievement.maxProgress
        );
        break;

      case 'conditional':
        if (this.checkConditionalCriteria(achievement, metadata)) {
          userAchievement.progress = Math.min(
            userAchievement.progress + value,
            userAchievement.maxProgress
          );
        }
        break;

      case 'time_based':
        if (this.checkTimeCriteria(achievement)) {
          userAchievement.progress = userAchievement.maxProgress;
        }
        break;

      case 'streak':
        this.updateStreakProgress(achievement, value);
        break;
    }

    // Check if achievement is unlocked
    if (userAchievement.progress >= userAchievement.maxProgress) {
      await this.unlockAchievement(userAchievement);
    }

    // Update in user progress
    this.userProgress.achievements.set(achievement.id, userAchievement);
  }

  private checkConditionalCriteria(achievement: Achievement, metadata?: Record<string, any>): boolean {
    if (!achievement.criteria.conditions || !metadata) return false;

    // Example: Check if all required connection methods have been used
    if (achievement.id === 'network_master' && metadata.connectionMethod) {
      const usedMethods = this.actionCounters.get('connection_methods_used') || 0;
      return usedMethods < achievement.criteria.target;
    }

    return true;
  }

  private checkTimeCriteria(achievement: Achievement): boolean {
    if (!achievement.criteria.timeframe) return false;

    const installTime = this.actionCounters.get('app_install_time') || Date.now();
    const timeElapsed = Date.now() - installTime;
    
    return timeElapsed <= achievement.criteria.timeframe;
  }

  private updateStreakProgress(achievement: Achievement, value: number): void {
    const streakData = this.streakCounters.get(achievement.id) || { count: 0, lastAction: 0 };
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;

    // Check if streak continues (within 24 hours)
    if (now - streakData.lastAction <= dayInMs) {
      streakData.count += value;
    } else {
      streakData.count = value; // Reset streak
    }

    streakData.lastAction = now;
    this.streakCounters.set(achievement.id, streakData);

    // Update achievement progress
    const userAchievement = this.userProgress?.achievements.get(achievement.id);
    if (userAchievement) {
      userAchievement.progress = Math.min(streakData.count, userAchievement.maxProgress);
    }
  }

  private async unlockAchievement(achievement: Achievement): Promise<void> {
    if (!this.userProgress) return;

    achievement.isUnlocked = true;
    achievement.unlockedAt = Date.now();

    // Update user stats
    this.userProgress.totalPoints += achievement.reward.points;
    this.userProgress.unlockedCount += 1;

    // Emit achievement unlocked event
    this.emit('achievementUnlocked', achievement);
    
    console.log(`🏆 Achievement unlocked: ${achievement.name} (+${achievement.reward.points} points)`);
  }

  // Public API methods
  getUserProgress(): UserAchievementProgress | null {
    return this.userProgress;
  }

  getAchievements(): Achievement[] {
    if (!this.userProgress) return [];
    return Array.from(this.userProgress.achievements.values());
  }

  getUnlockedAchievements(): Achievement[] {
    return this.getAchievements().filter(a => a.isUnlocked);
  }

  getAchievementsByCategory(category: AchievementCategory): Achievement[] {
    return this.getAchievements().filter(a => a.category === category);
  }

  getTotalPoints(): number {
    return this.userProgress?.totalPoints || 0;
  }

  getCompletionPercentage(): number {
    const achievements = this.getAchievements();
    if (achievements.length === 0) return 0;
    
    const unlockedCount = achievements.filter(a => a.isUnlocked).length;
    return Math.round((unlockedCount / achievements.length) * 100);
  }

  // Convenience methods for common actions
  async trackConnection(deviceId: string, method: string): Promise<void> {
    await this.trackAction('device_connected', 1);
    await this.trackAction('unique_device_connected', 1, { deviceId });
    await this.trackAction('connection_methods_used', 1, { connectionMethod: method });
  }

  async trackGameSession(gameId: string, duration: number, result?: 'won' | 'lost' | 'draw'): Promise<void> {
    await this.trackAction('game_session_started', 1, { gameId });
    await this.trackAction('playtime_minutes', Math.round(duration / 60000));
    
    if (result === 'won') {
      await this.trackAction('sessions_won', 1);
    } else if (result === 'lost') {
      await this.trackAction('sessions_lost', 1);
    }
  }

  async trackSessionHosted(): Promise<void> {
    await this.trackAction('sessions_hosted', 1);
  }

  async trackSessionJoined(): Promise<void> {
    await this.trackAction('sessions_joined', 1);
  }
}

export default AchievementManager.getInstance();
