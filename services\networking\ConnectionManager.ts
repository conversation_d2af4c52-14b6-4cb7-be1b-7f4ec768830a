import { Platform } from 'react-native';
import BluetoothService, { BluetoothDevice } from './BluetoothService';
import WiFiDirectService, { WiFiDirectPeer } from './WiFiDirectService';
import * as Network from 'expo-network';

export type ConnectionMethod = 'bluetooth' | 'wifi-direct' | 'lan' | 'auto';

export interface ConnectedDevice {
  id: string;
  name: string;
  address: string;
  method: ConnectionMethod;
  connected: boolean;
  lastSeen: Date;
  signalStrength?: number;
}

export interface ConnectionStatus {
  isConnected: boolean;
  method: ConnectionMethod;
  deviceCount: number;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  latency?: number;
}

export interface ConnectionManagerInterface {
  initialize(): Promise<boolean>;
  startDiscovery(method?: ConnectionMethod): Promise<void>;
  stopDiscovery(): Promise<void>;
  connectToDevice(deviceId: string, method?: ConnectionMethod): Promise<boolean>;
  disconnectFromDevice(deviceId: string): Promise<boolean>;
  disconnectAll(): Promise<void>;
  getAvailableDevices(): Promise<ConnectedDevice[]>;
  getConnectedDevices(): ConnectedDevice[];
  getConnectionStatus(): ConnectionStatus;
  sendData(deviceId: string, data: any): Promise<boolean>;
  broadcastData(data: any): Promise<boolean>;
  onDeviceDiscovered(callback: (device: ConnectedDevice) => void): void;
  onDeviceConnected(callback: (device: ConnectedDevice) => void): void;
  onDeviceDisconnected(callback: (device: ConnectedDevice) => void): void;
  onDataReceived(callback: (deviceId: string, data: any) => void): void;
  onConnectionStatusChanged(callback: (status: ConnectionStatus) => void): void;
}

class ConnectionManager implements ConnectionManagerInterface {
  private connectedDevices: Map<string, ConnectedDevice> = new Map();
  private availableDevices: Map<string, ConnectedDevice> = new Map();
  private currentMethod: ConnectionMethod = 'auto';
  private isInitialized = false;

  // Event callbacks
  private deviceDiscoveredCallback?: (device: ConnectedDevice) => void;
  private deviceConnectedCallback?: (device: ConnectedDevice) => void;
  private deviceDisconnectedCallback?: (device: ConnectedDevice) => void;
  private dataReceivedCallback?: (deviceId: string, data: any) => void;
  private connectionStatusChangedCallback?: (status: ConnectionStatus) => void;

  constructor() {
    this.setupServiceListeners();
  }

  private setupServiceListeners(): void {
    // Bluetooth service listeners
    BluetoothService.onDeviceDiscovered((device: BluetoothDevice) => {
      const connectedDevice: ConnectedDevice = {
        id: device.id,
        name: device.name,
        address: device.address,
        method: 'bluetooth',
        connected: device.connected,
        lastSeen: new Date(),
        signalStrength: device.rssi,
      };

      this.availableDevices.set(device.id, connectedDevice);
      this.deviceDiscoveredCallback?.(connectedDevice);
    });

    BluetoothService.onDeviceConnected((device: BluetoothDevice) => {
      const connectedDevice: ConnectedDevice = {
        id: device.id,
        name: device.name,
        address: device.address,
        method: 'bluetooth',
        connected: true,
        lastSeen: new Date(),
      };

      this.connectedDevices.set(device.id, connectedDevice);
      this.deviceConnectedCallback?.(connectedDevice);
      this.notifyConnectionStatusChanged();
    });

    BluetoothService.onDeviceDisconnected((device: BluetoothDevice) => {
      const connectedDevice = this.connectedDevices.get(device.id);
      if (connectedDevice) {
        connectedDevice.connected = false;
        this.connectedDevices.delete(device.id);
        this.deviceDisconnectedCallback?.(connectedDevice);
        this.notifyConnectionStatusChanged();
      }
    });

    BluetoothService.onDataReceived((deviceId: string, data: string) => {
      try {
        const parsedData = JSON.parse(data);
        this.dataReceivedCallback?.(deviceId, parsedData);
      } catch (error) {
        this.dataReceivedCallback?.(deviceId, data);
      }
    });

    // WiFi Direct service listeners (Android only)
    if (Platform.OS === 'android') {
      WiFiDirectService.onPeersChanged((peers: WiFiDirectPeer[]) => {
        peers.forEach(peer => {
          const connectedDevice: ConnectedDevice = {
            id: peer.deviceAddress,
            name: peer.deviceName,
            address: peer.deviceAddress,
            method: 'wifi-direct',
            connected: false,
            lastSeen: new Date(),
          };

          this.availableDevices.set(peer.deviceAddress, connectedDevice);
          this.deviceDiscoveredCallback?.(connectedDevice);
        });
      });

      WiFiDirectService.onConnectionChanged((connectionInfo: any) => {
        if (connectionInfo.isConnected) {
          // Handle WiFi Direct connection
          this.notifyConnectionStatusChanged();
        }
      });

      WiFiDirectService.onDataReceived((data: string) => {
        try {
          const parsedData = JSON.parse(data);
          this.dataReceivedCallback?.('wifi-direct', parsedData);
        } catch (error) {
          this.dataReceivedCallback?.('wifi-direct', data);
        }
      });
    }
  }

  async initialize(): Promise<boolean> {
    try {
      // Initialize Bluetooth
      const bluetoothPermissions = await BluetoothService.requestPermissions();
      if (!bluetoothPermissions) {
        console.warn('Bluetooth permissions not granted');
      }

      // Initialize WiFi Direct (Android only)
      if (Platform.OS === 'android') {
        const wifiDirectPermissions = await WiFiDirectService.requestPermissions();
        if (!wifiDirectPermissions) {
          console.warn('WiFi Direct permissions not granted');
        }

        const wifiDirectInitialized = await WiFiDirectService.initialize();
        if (!wifiDirectInitialized) {
          console.warn('WiFi Direct initialization failed');
        }
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing ConnectionManager:', error);
      return false;
    }
  }

  async startDiscovery(method: ConnectionMethod = 'auto'): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('ConnectionManager not initialized');
    }

    console.log(`Starting enhanced device discovery with method: ${method}`);
    this.currentMethod = method;
    this.availableDevices.clear();

    try {
      // Enhanced Bluetooth discovery
      if (method === 'bluetooth' || method === 'auto') {
        await this.startBluetoothDiscovery();
      }

      // Enhanced WiFi Direct discovery (Android)
      if ((method === 'wifi-direct' || method === 'auto') && Platform.OS === 'android') {
        await this.startWiFiDirectDiscovery();
      }

      // Enhanced WiFi network scanning
      if (method === 'wifi' || method === 'auto') {
        await this.startWiFiNetworkScanning();
      }

      // Start continuous device monitoring
      this.startDeviceMonitoring();

    } catch (error) {
      console.error('Error starting enhanced discovery:', error);
      throw error;
    }
  }

  private async startBluetoothDiscovery(): Promise<void> {
    try {
      const bluetoothEnabled = await BluetoothService.isEnabled();
      if (bluetoothEnabled) {
        console.log('Starting Bluetooth discovery...');
        await BluetoothService.startDiscovery();

        // Start signal strength monitoring for discovered devices
        this.startBluetoothSignalMonitoring();
      } else {
        console.log('Requesting Bluetooth enable...');
        const enabled = await BluetoothService.requestEnable();
        if (enabled) {
          await BluetoothService.startDiscovery();
          this.startBluetoothSignalMonitoring();
        } else {
          console.warn('Bluetooth not enabled, skipping Bluetooth discovery');
        }
      }
    } catch (error) {
      console.error('Bluetooth discovery failed:', error);
    }
  }

  private async startWiFiDirectDiscovery(): Promise<void> {
    try {
      if (WiFiDirectService.isAvailable()) {
        console.log('Starting WiFi Direct discovery...');
        await WiFiDirectService.startPeerDiscovery();

        // Start WiFi Direct quality monitoring
        this.startWiFiDirectMonitoring();
      } else {
        console.warn('WiFi Direct not available');
      }
    } catch (error) {
      console.error('WiFi Direct discovery failed:', error);
    }
  }

  private async startWiFiNetworkScanning(): Promise<void> {
    try {
      console.log('Starting WiFi network scanning...');

      // Scan for gaming-optimized networks and hotspots
      const networks = await this.scanForGamingNetworks();

      networks.forEach(network => {
        const device: ConnectedDevice = {
          id: network.ssid || `network_${Date.now()}`,
          name: network.ssid || 'Unknown Network',
          address: network.bssid || '',
          method: 'wifi',
          connected: false,
          lastSeen: new Date(),
          signalStrength: network.level || -70,
          networkInfo: {
            ssid: network.ssid,
            security: network.capabilities,
            frequency: network.frequency,
            isGamingOptimized: this.isGamingOptimizedNetwork(network)
          }
        };

        this.availableDevices.set(device.id, device);
        this.deviceDiscoveredCallback?.(device);
      });
    } catch (error) {
      console.error('WiFi network scanning failed:', error);
    }
  }

  private async scanForGamingNetworks(): Promise<any[]> {
    try {
      // Simulate WiFi network scanning
      // In real implementation, this would use platform-specific WiFi APIs
      const mockNetworks = [
        {
          ssid: 'Gaming_Network_5G',
          bssid: '00:11:22:33:44:55',
          level: -45,
          frequency: 5180,
          capabilities: '[WPA2-PSK-CCMP][ESS]'
        },
        {
          ssid: 'ROG_Gaming_Hub',
          bssid: '00:11:22:33:44:56',
          level: -55,
          frequency: 5240,
          capabilities: '[WPA3-SAE-CCMP][ESS]'
        },
        {
          ssid: 'Mobile_Hotspot_Gaming',
          bssid: '00:11:22:33:44:57',
          level: -65,
          frequency: 2437,
          capabilities: '[WPA2-PSK-CCMP][ESS]'
        }
      ];

      // Filter networks that appear to be gaming-related
      return mockNetworks.filter(network =>
        this.isGamingOptimizedNetwork(network)
      );
    } catch (error) {
      console.error('Failed to scan for gaming networks:', error);
      return [];
    }
  }

  private isGamingOptimizedNetwork(network: any): boolean {
    const ssid = network.ssid?.toLowerCase() || '';
    const gamingKeywords = ['gaming', 'rog', 'alienware', 'razer', 'msi', 'game', 'esports'];

    return gamingKeywords.some(keyword => ssid.includes(keyword)) ||
           network.frequency > 5000 || // 5GHz networks are generally better for gaming
           network.level > -50; // Strong signal strength
  }

  private startBluetoothSignalMonitoring(): void {
    // Monitor Bluetooth signal strength every 5 seconds
    const monitoringInterval = setInterval(async () => {
      try {
        for (const [deviceId, device] of this.availableDevices) {
          if (device.method === 'bluetooth') {
            // Update signal strength
            const updatedStrength = await this.measureBluetoothSignalStrength(deviceId);
            if (updatedStrength !== null) {
              device.signalStrength = updatedStrength;
              device.lastSeen = new Date();
            }
          }
        }
      } catch (error) {
        console.error('Bluetooth signal monitoring error:', error);
      }
    }, 5000);

    // Store interval for cleanup
    this.monitoringIntervals.set('bluetooth', monitoringInterval);
  }

  private startWiFiDirectMonitoring(): void {
    // Monitor WiFi Direct connection quality
    const monitoringInterval = setInterval(async () => {
      try {
        for (const [deviceId, device] of this.availableDevices) {
          if (device.method === 'wifi-direct') {
            // Update connection quality metrics
            const quality = await this.assessWiFiDirectQuality(deviceId);
            device.connectionQuality = quality;
            device.lastSeen = new Date();
          }
        }
      } catch (error) {
        console.error('WiFi Direct monitoring error:', error);
      }
    }, 3000);

    this.monitoringIntervals.set('wifi-direct', monitoringInterval);
  }

  private startDeviceMonitoring(): void {
    // General device monitoring and cleanup
    const monitoringInterval = setInterval(() => {
      const now = Date.now();
      const staleThreshold = 30000; // 30 seconds

      // Remove stale devices
      for (const [deviceId, device] of this.availableDevices) {
        if (now - device.lastSeen.getTime() > staleThreshold) {
          this.availableDevices.delete(deviceId);
          console.log(`Removed stale device: ${device.name}`);
        }
      }
    }, 10000);

    this.monitoringIntervals.set('general', monitoringInterval);
  }

  private async measureBluetoothSignalStrength(deviceId: string): Promise<number | null> {
    try {
      // In real implementation, this would query the Bluetooth adapter
      // For now, simulate signal strength with some variation
      const baseStrength = -60 + (Math.random() - 0.5) * 20; // -70 to -50 dBm
      return Math.round(baseStrength);
    } catch (error) {
      return null;
    }
  }

  private async assessWiFiDirectQuality(deviceId: string): Promise<'excellent' | 'good' | 'fair' | 'poor'> {
    try {
      // Simulate quality assessment based on various factors
      const signalStrength = Math.random() * 60 - 80; // -80 to -20 dBm
      const latency = Math.random() * 100; // 0-100ms

      if (signalStrength > -40 && latency < 20) return 'excellent';
      if (signalStrength > -60 && latency < 50) return 'good';
      if (signalStrength > -75 && latency < 80) return 'fair';
      return 'poor';
    } catch (error) {
      return 'poor';
    }
  }

  private monitoringIntervals = new Map<string, NodeJS.Timeout>();

  async stopDiscovery(): Promise<void> {
    try {
      console.log('Stopping enhanced device discovery...');

      // Stop Bluetooth discovery
      await BluetoothService.stopDiscovery();

      // Stop WiFi Direct discovery (Android)
      if (Platform.OS === 'android' && WiFiDirectService.isAvailable()) {
        await WiFiDirectService.stopPeerDiscovery();
      }

      // Clean up all monitoring intervals
      this.stopAllMonitoring();

      console.log('Enhanced device discovery stopped');
    } catch (error) {
      console.error('Error stopping enhanced discovery:', error);
      throw error;
    }
  }

  private stopAllMonitoring(): void {
    // Clear all monitoring intervals
    for (const [name, interval] of this.monitoringIntervals) {
      clearInterval(interval);
      console.log(`Stopped ${name} monitoring`);
    }
    this.monitoringIntervals.clear();
  }

  async connectToDevice(deviceId: string, method?: ConnectionMethod): Promise<boolean> {
    const device = this.availableDevices.get(deviceId);
    if (!device) {
      console.error(`Device ${deviceId} not found in available devices`);
      return false;
    }

    const connectionMethod = method || device.method;
    console.log(`Attempting to connect to device ${device.name} via ${connectionMethod}`);

    try {
      // Pre-connection validation
      const validationResult = await this.validateConnectionAttempt(device, connectionMethod);
      if (!validationResult.valid) {
        console.error(`Connection validation failed: ${validationResult.reason}`);
        return false;
      }

      // Perform connection with enhanced error handling and retry logic
      const success = await this.performEnhancedConnection(device, connectionMethod);

      if (success) {
        // Post-connection setup
        await this.setupConnectedDevice(device, connectionMethod);
        console.log(`Successfully connected to ${device.name}`);
        return true;
      } else {
        console.error(`Failed to connect to ${device.name}`);
        return false;
      }
    } catch (error) {
      console.error('Error connecting to device:', error);
      return false;
    }
  }

  private async validateConnectionAttempt(
    device: ConnectedDevice,
    method: ConnectionMethod
  ): Promise<{ valid: boolean; reason?: string }> {
    // Check if device is already connected
    if (device.connected) {
      return { valid: false, reason: 'Device already connected' };
    }

    // Check signal strength
    if (device.signalStrength && device.signalStrength < -85) {
      return { valid: false, reason: 'Signal strength too weak' };
    }

    // Check method-specific requirements
    switch (method) {
      case 'bluetooth':
        const bluetoothEnabled = await BluetoothService.isEnabled();
        if (!bluetoothEnabled) {
          return { valid: false, reason: 'Bluetooth not enabled' };
        }
        break;

      case 'wifi-direct':
        if (Platform.OS !== 'android') {
          return { valid: false, reason: 'WiFi Direct only available on Android' };
        }
        if (!WiFiDirectService.isAvailable()) {
          return { valid: false, reason: 'WiFi Direct not available' };
        }
        break;

      case 'wifi':
        // Check if network is secure and accessible
        if (device.networkInfo?.security && !device.networkInfo.security.includes('NONE')) {
          // Would need password for secure networks
          console.warn('Secure network detected - may require password');
        }
        break;
    }

    return { valid: true };
  }

  private async performEnhancedConnection(
    device: ConnectedDevice,
    method: ConnectionMethod
  ): Promise<boolean> {
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        attempt++;
        console.log(`Connection attempt ${attempt}/${maxRetries} for ${device.name}`);

        let success = false;

        switch (method) {
          case 'bluetooth':
            success = await this.connectViaBluetooth(device);
            break;
          case 'wifi-direct':
            success = await this.connectViaWiFiDirect(device);
            break;
          case 'wifi':
            success = await this.connectViaWiFi(device);
            break;
          default:
            console.error(`Unsupported connection method: ${method}`);
            return false;
        }

        if (success) {
          return true;
        }

        // Wait before retry
        if (attempt < maxRetries) {
          const delay = attempt * 2000; // Exponential backoff
          console.log(`Retrying connection in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

      } catch (error) {
        console.error(`Connection attempt ${attempt} failed:`, error);
      }
    }

    return false;
  }

  private async connectViaBluetooth(device: ConnectedDevice): Promise<boolean> {
    try {
      // Enhanced Bluetooth connection with pairing check
      const isPaired = await BluetoothService.isPaired(device.id);
      if (!isPaired) {
        console.log('Device not paired, attempting to pair...');
        const paired = await BluetoothService.pairDevice(device.id);
        if (!paired) {
          console.error('Failed to pair with device');
          return false;
        }
      }

      return await BluetoothService.connectToDevice(device.id);
    } catch (error) {
      console.error('Bluetooth connection failed:', error);
      return false;
    }
  }

  private async connectViaWiFiDirect(device: ConnectedDevice): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // Enhanced WiFi Direct connection
        const connected = await WiFiDirectService.connectToPeer(device.address);

        if (connected) {
          // Verify connection establishment
          const connectionInfo = await WiFiDirectService.getConnectionInfo();
          return connectionInfo?.isConnected || false;
        }
      }
      return false;
    } catch (error) {
      console.error('WiFi Direct connection failed:', error);
      return false;
    }
  }

  private async connectViaWiFi(device: ConnectedDevice): Promise<boolean> {
    try {
      // Enhanced WiFi network connection
      if (!device.networkInfo?.ssid) {
        console.error('No SSID available for WiFi connection');
        return false;
      }

      // In real implementation, this would use platform-specific WiFi APIs
      // For now, simulate connection success based on signal strength
      const signalStrength = device.signalStrength || -70;
      const connectionProbability = Math.max(0, (signalStrength + 100) / 100);

      return Math.random() < connectionProbability;
    } catch (error) {
      console.error('WiFi connection failed:', error);
      return false;
    }
  }

  private async setupConnectedDevice(device: ConnectedDevice, method: ConnectionMethod): Promise<void> {
    // Update device state
    device.connected = true;
    device.method = method;
    device.lastSeen = new Date();
    device.connectionEstablishedAt = new Date();

    // Move to connected devices
    this.connectedDevices.set(device.id, device);

    // Start connection quality monitoring
    this.startConnectionQualityMonitoring(device.id);

    // Perform initial handshake
    await this.performConnectionHandshake(device);

    // Notify listeners
    this.deviceConnectedCallback?.(device);
    this.notifyConnectionStatusChanged();
  }

  private startConnectionQualityMonitoring(deviceId: string): void {
    const monitoringInterval = setInterval(async () => {
      const device = this.connectedDevices.get(deviceId);
      if (!device || !device.connected) {
        clearInterval(monitoringInterval);
        return;
      }

      try {
        // Update connection metrics
        const quality = await this.assessConnectionQuality(device);
        device.connectionQuality = quality;

        // Check for connection issues
        if (quality === 'poor') {
          console.warn(`Poor connection quality detected for ${device.name}`);
          // Could trigger automatic reconnection or optimization
        }

        device.lastSeen = new Date();
      } catch (error) {
        console.error('Connection quality monitoring error:', error);
      }
    }, 5000);

    this.monitoringIntervals.set(`connection_${deviceId}`, monitoringInterval);
  }

  private async assessConnectionQuality(device: ConnectedDevice): Promise<'excellent' | 'good' | 'fair' | 'poor'> {
    try {
      // Assess quality based on multiple factors
      const signalStrength = device.signalStrength || -70;
      const timeSinceLastSeen = Date.now() - device.lastSeen.getTime();

      // Simulate latency measurement
      const latency = await this.measureConnectionLatency(device.id);

      // Calculate overall quality score
      let score = 100;

      // Signal strength factor (0-40 points)
      score += Math.max(-40, (signalStrength + 100) * 0.4);

      // Latency factor (0-30 points)
      score -= Math.min(30, latency * 0.3);

      // Stability factor (0-30 points)
      score -= Math.min(30, timeSinceLastSeen / 1000);

      if (score >= 80) return 'excellent';
      if (score >= 60) return 'good';
      if (score >= 40) return 'fair';
      return 'poor';
    } catch (error) {
      return 'poor';
    }
  }

  private async measureConnectionLatency(deviceId: string): Promise<number> {
    try {
      // Simulate latency measurement by sending a ping
      const startTime = Date.now();

      // In real implementation, this would send actual ping packets
      await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));

      return Date.now() - startTime;
    } catch (error) {
      return 100; // Default high latency on error
    }
  }

  private async performConnectionHandshake(device: ConnectedDevice): Promise<void> {
    try {
      // Send initial handshake message
      const handshakeData = {
        type: 'handshake',
        deviceId: 'local-device', // Would be actual device ID
        timestamp: Date.now(),
        capabilities: ['gaming', 'real-time-sync'],
        protocolVersion: '1.0'
      };

      await this.sendData(device.id, handshakeData);
      console.log(`Handshake sent to ${device.name}`);
    } catch (error) {
      console.error('Handshake failed:', error);
    }
  }

  async disconnectFromDevice(deviceId: string): Promise<boolean> {
    const device = this.connectedDevices.get(deviceId);
    if (!device) {
      return false;
    }

    try {
      let success = false;

      switch (device.method) {
        case 'bluetooth':
          success = await BluetoothService.disconnectFromDevice(deviceId);
          break;
        case 'wifi-direct':
          if (Platform.OS === 'android') {
            success = await WiFiDirectService.disconnect();
          }
          break;
        default:
          return false;
      }

      if (success) {
        device.connected = false;
        this.connectedDevices.delete(deviceId);
        this.notifyConnectionStatusChanged();
      }

      return success;
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      return false;
    }
  }

  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.connectedDevices.keys()).map(
      deviceId => this.disconnectFromDevice(deviceId)
    );

    await Promise.all(disconnectPromises);
  }

  async getAvailableDevices(): Promise<ConnectedDevice[]> {
    return Array.from(this.availableDevices.values());
  }

  getConnectedDevices(): ConnectedDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  getConnectionStatus(): ConnectionStatus {
    const deviceCount = this.connectedDevices.size;
    const isConnected = deviceCount > 0;

    // Determine connection quality based on device count and signal strength
    let quality: 'excellent' | 'good' | 'fair' | 'poor' = 'poor';
    if (deviceCount > 0) {
      const avgSignalStrength = Array.from(this.connectedDevices.values())
        .filter(device => device.signalStrength !== undefined)
        .reduce((sum, device) => sum + (device.signalStrength || 0), 0) / deviceCount;

      if (avgSignalStrength > -50) quality = 'excellent';
      else if (avgSignalStrength > -70) quality = 'good';
      else if (avgSignalStrength > -85) quality = 'fair';
    }

    return {
      isConnected,
      method: this.currentMethod,
      deviceCount,
      quality,
    };
  }

  async sendData(deviceId: string, data: any): Promise<boolean> {
    const device = this.connectedDevices.get(deviceId);
    if (!device) {
      return false;
    }

    try {
      const serializedData = JSON.stringify(data);

      switch (device.method) {
        case 'bluetooth':
          return await BluetoothService.sendData(deviceId, serializedData);
        case 'wifi-direct':
          if (Platform.OS === 'android') {
            return await WiFiDirectService.sendData(serializedData);
          }
          break;
      }

      return false;
    } catch (error) {
      console.error('Error sending data:', error);
      return false;
    }
  }

  async broadcastData(data: any): Promise<boolean> {
    const sendPromises = Array.from(this.connectedDevices.keys()).map(
      deviceId => this.sendData(deviceId, data)
    );

    const results = await Promise.all(sendPromises);
    return results.some(result => result);
  }

  private notifyConnectionStatusChanged(): void {
    const status = this.getConnectionStatus();
    this.connectionStatusChangedCallback?.(status);
  }

  onDeviceDiscovered(callback: (device: ConnectedDevice) => void): void {
    this.deviceDiscoveredCallback = callback;
  }

  onDeviceConnected(callback: (device: ConnectedDevice) => void): void {
    this.deviceConnectedCallback = callback;
  }

  onDeviceDisconnected(callback: (device: ConnectedDevice) => void): void {
    this.deviceDisconnectedCallback = callback;
  }

  onDataReceived(callback: (deviceId: string, data: any) => void): void {
    this.dataReceivedCallback = callback;
  }

  onConnectionStatusChanged(callback: (status: ConnectionStatus) => void): void {
    this.connectionStatusChangedCallback = callback;
  }
}

export default new ConnectionManager();
