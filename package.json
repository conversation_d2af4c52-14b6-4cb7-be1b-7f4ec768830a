{"name": "logaco", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --format json --output-file eslint-results.json", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "build": "expo export", "build:web": "expo export --platform web", "build:android": "expo export --platform android", "build:ios": "expo export --platform ios", "test:smoke": "jest --testPathPattern=smoke --passWithNoTests", "analyze": "npx expo export --platform web && npx bundlesize", "clean": "expo r -c", "postinstall": "expo install --fix"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.9", "expo-barcode-scanner": "^13.0.1", "expo-blur": "^14.1.4", "expo-camera": "^16.1.6", "expo-clipboard": "^7.1.4", "expo-constants": "^17.1.6", "expo-font": "^13.3.1", "expo-linear-gradient": "^14.1.4", "expo-linking": "^7.1.5", "expo-router": "^5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "expo-device": "~7.1.4", "expo-notifications": "~0.31.2", "expo-store-review": "~8.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "prettier": "^3.2.5", "bundlesize": "^0.18.1", "@testing-library/react-native": "^12.4.3", "@testing-library/jest-native": "^5.4.3", "jest-junit": "^16.0.0"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js", "@testing-library/jest-native/extend-expect"], "testMatch": ["**/__tests__/**/*.test.{js,jsx,ts,tsx}", "**/*.test.{js,jsx,ts,tsx}"], "collectCoverageFrom": ["**/*.{js,jsx,ts,tsx}", "!**/node_modules/**", "!**/coverage/**", "!**/*.config.js", "!**/expo-env.d.ts", "!**/__tests__/**", "!**/jest-setup.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "coverageReporters": ["text", "lcov", "html", "json-summary"], "testResultsProcessor": "jest-junit", "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|expo|@expo|react-native-bluetooth-classic|react-native-wifi-p2p)/)"]}, "bundlesize": [{"path": "dist/static/js/*.js", "maxSize": "2MB"}, {"path": "dist/static/css/*.css", "maxSize": "500KB"}], "private": true}