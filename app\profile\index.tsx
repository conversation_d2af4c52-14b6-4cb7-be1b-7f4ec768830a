import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import AchievementManager from '../../services/achievements/AchievementManager';
import LeaderboardManager from '../../services/leaderboards/LeaderboardManager';
import SocialManager from '../../services/social/SocialManager';

const { width } = Dimensions.get('window');

export default function ProfileScreen() {
  const userProfile = useSelector((state: RootState) => state.user.profile);
  const userStats = useSelector((state: RootState) => state.user.stats);
  
  const [achievements, setAchievements] = useState<any[]>([]);
  const [rankings, setRankings] = useState<any[]>([]);
  const [socialStats, setSocialStats] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfileData();
  }, []);

  const loadProfileData = async () => {
    try {
      setLoading(true);

      // Initialize services if needed
      if (userProfile?.id) {
        await AchievementManager.initialize(userProfile.id);
        await LeaderboardManager.initialize(userProfile.id);
        await SocialManager.initialize(userProfile.id);

        // Load achievements
        const userAchievements = AchievementManager.getUnlockedAchievements();
        setAchievements(userAchievements.slice(0, 3)); // Show top 3

        // Load rankings
        const userRankings = LeaderboardManager.getCurrentPlayerRankings();
        setRankings(userRankings.slice(0, 3)); // Show top 3

        // Load social stats
        const stats = SocialManager.getSocialStats();
        setSocialStats(stats);
      }
    } catch (error) {
      console.error('Failed to load profile data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return '#00FF88';
      case 'away': return '#FFB800';
      case 'busy': return '#FF4444';
      default: return '#888888';
    }
  };

  const formatPlaytime = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const StatCard = ({ title, value, icon, onPress }: any) => (
    <TouchableOpacity style={styles.statCard} onPress={onPress}>
      <BlurView intensity={15} style={styles.statBlur}>
        <Ionicons name={icon} size={24} color="#00D4FF" />
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
      </BlurView>
    </TouchableOpacity>
  );

  const AchievementBadge = ({ achievement }: any) => (
    <View style={styles.achievementBadge}>
      <BlurView intensity={15} style={styles.achievementBlur}>
        <Ionicons name={achievement.icon} size={20} color="#FFD700" />
        <Text style={styles.achievementName}>{achievement.name}</Text>
      </BlurView>
    </View>
  );

  return (
    <LinearGradient colors={['#0A0A0A', '#1A1A2E', '#16213E']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Header */}
          <BlurView intensity={15} style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                {userProfile?.avatar ? (
                  <Image source={{ uri: userProfile.avatar }} style={styles.avatarImage} />
                ) : (
                  <Ionicons name="person" size={48} color="#00D4FF" />
                )}
              </View>
              <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(userProfile?.status || 'offline') }]} />
            </View>
            
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{userProfile?.name || 'Player'}</Text>
              <Text style={styles.profileDevice}>{userProfile?.deviceName || 'Unknown Device'}</Text>
              <Text style={styles.profileStatus}>
                {userProfile?.status?.charAt(0).toUpperCase() + (userProfile?.status?.slice(1) || 'offline')}
              </Text>
            </View>

            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => router.push('/profile/edit')}
            >
              <Ionicons name="pencil" size={20} color="#00D4FF" />
            </TouchableOpacity>
          </BlurView>

          {/* Stats Grid */}
          <View style={styles.statsGrid}>
            <StatCard
              title="Games Played"
              value={userStats?.gamesPlayed || 0}
              icon="game-controller"
              onPress={() => router.push('/profile/stats')}
            />
            <StatCard
              title="Win Rate"
              value={`${Math.round(userStats?.winRate || 0)}%`}
              icon="trophy"
              onPress={() => router.push('/profile/stats')}
            />
            <StatCard
              title="Playtime"
              value={formatPlaytime(userStats?.totalPlaytime || 0)}
              icon="time"
              onPress={() => router.push('/profile/stats')}
            />
            <StatCard
              title="Achievements"
              value={achievements.length}
              icon="medal"
              onPress={() => router.push('/profile/achievements')}
            />
          </View>

          {/* Recent Achievements */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Achievements</Text>
              <TouchableOpacity onPress={() => router.push('/profile/achievements')}>
                <Text style={styles.sectionLink}>View All</Text>
              </TouchableOpacity>
            </View>
            
            {achievements.length > 0 ? (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {achievements.map((achievement, index) => (
                  <AchievementBadge key={index} achievement={achievement} />
                ))}
              </ScrollView>
            ) : (
              <BlurView intensity={15} style={styles.emptyCard}>
                <Ionicons name="medal-outline" size={32} color="#666" />
                <Text style={styles.emptyText}>No achievements yet</Text>
                <Text style={styles.emptySubtext}>Start playing to unlock achievements!</Text>
              </BlurView>
            )}
          </View>

          {/* Leaderboard Rankings */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Leaderboard Rankings</Text>
              <TouchableOpacity onPress={() => router.push('/profile/leaderboards')}>
                <Text style={styles.sectionLink}>View All</Text>
              </TouchableOpacity>
            </View>
            
            {rankings.length > 0 ? (
              <View style={styles.rankingsContainer}>
                {rankings.map((ranking, index) => (
                  <BlurView key={index} intensity={15} style={styles.rankingCard}>
                    <View style={styles.rankingInfo}>
                      <Text style={styles.rankingTitle}>
                        {ranking.leaderboardId.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </Text>
                      <Text style={styles.rankingPosition}>
                        #{ranking.rank} of {ranking.total}
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={20} color="#666" />
                  </BlurView>
                ))}
              </View>
            ) : (
              <BlurView intensity={15} style={styles.emptyCard}>
                <Ionicons name="podium-outline" size={32} color="#666" />
                <Text style={styles.emptyText}>No rankings yet</Text>
                <Text style={styles.emptySubtext}>Play games to appear on leaderboards!</Text>
              </BlurView>
            )}
          </View>

          {/* Social Stats */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Social</Text>
            
            <View style={styles.socialGrid}>
              <TouchableOpacity 
                style={styles.socialCard}
                onPress={() => router.push('/profile/friends')}
              >
                <BlurView intensity={15} style={styles.socialBlur}>
                  <Ionicons name="people" size={24} color="#00D4FF" />
                  <Text style={styles.socialValue}>{socialStats.friendCount || 0}</Text>
                  <Text style={styles.socialTitle}>Friends</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.socialCard}
                onPress={() => router.push('/profile/groups')}
              >
                <BlurView intensity={15} style={styles.socialBlur}>
                  <Ionicons name="people-circle" size={24} color="#00D4FF" />
                  <Text style={styles.socialValue}>{socialStats.groupCount || 0}</Text>
                  <Text style={styles.socialTitle}>Groups</Text>
                </BlurView>
              </TouchableOpacity>
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            
            <View style={styles.actionsGrid}>
              <TouchableOpacity 
                style={styles.actionCard}
                onPress={() => router.push('/profile/edit')}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="person-circle" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Edit Profile</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionCard}
                onPress={() => router.push('/profile/avatar')}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="image" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Change Avatar</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.actionCard}
                onPress={() => router.push('/(tabs)/settings')}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="settings" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Settings</Text>
                </BlurView>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 100,
  },
  profileHeader: {
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#00D4FF',
  },
  avatarImage: {
    width: 76,
    height: 76,
    borderRadius: 38,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#000',
  },
  profileInfo: {
    flex: 1,
    marginLeft: 16,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  profileDevice: {
    fontSize: 16,
    color: '#CCCCCC',
    marginBottom: 2,
  },
  profileStatus: {
    fontSize: 14,
    color: '#00D4FF',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#00D4FF',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    width: (width - 60) / 2,
    marginBottom: 12,
  },
  statBlur: {
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    overflow: 'hidden',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 8,
  },
  statTitle: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  sectionLink: {
    fontSize: 14,
    color: '#00D4FF',
  },
  achievementBadge: {
    marginRight: 12,
  },
  achievementBlur: {
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    minWidth: 100,
    overflow: 'hidden',
  },
  achievementName: {
    fontSize: 12,
    color: '#FFFFFF',
    marginTop: 4,
    textAlign: 'center',
  },
  emptyCard: {
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    overflow: 'hidden',
  },
  emptyText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
    textAlign: 'center',
  },
  rankingsContainer: {
    gap: 8,
  },
  rankingCard: {
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  rankingInfo: {
    flex: 1,
  },
  rankingTitle: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  rankingPosition: {
    fontSize: 14,
    color: '#00D4FF',
    marginTop: 2,
  },
  socialGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  socialCard: {
    width: (width - 60) / 2,
  },
  socialBlur: {
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    overflow: 'hidden',
  },
  socialValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 8,
  },
  socialTitle: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - 80) / 3,
    marginBottom: 12,
  },
  actionBlur: {
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    overflow: 'hidden',
  },
  actionText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginTop: 8,
    textAlign: 'center',
  },
});
